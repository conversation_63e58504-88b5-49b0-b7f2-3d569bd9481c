/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { setStorageSync } from '@/utils/storage';
import { Button, Checkbox, Col, Dropdown, Menu, message, Row } from 'antd';
import React, { useEffect, useState } from 'react';

const list = [
  { key: 1, title: '每日入库量' },
  { key: 2, title: '3日出库率' },
  { key: 3, title: '每日寄件量' },
  { key: 4, title: '每日新增站点数' },
  { key: 5, title: '驿站数量' },
  { key: 6, title: '3日出库排名' },
  { key: 7, title: '昨日入库品牌占比' },
  { key: 8, title: '昨日入库量排名' },
  { key: 9, title: '驿站监控视频' },
];

const todayList = [
  { key: 10, title: '今日入库量' },
  { key: 11, title: '今日驿站入库量' },
  { key: 12, title: '今日入柜量' },
  { key: 13, title: '今日到件量' },
  { key: 14, title: '今日派件量' },
  { key: 15, title: '今日寄件量' },
  { key: 16, title: '今日快递费' },
  { key: 17, title: '今日出库量' },
  { key: 18, title: '今日取件量' },
];
export const dashboardSettingKey = 'kb-dashboardSetting';

// 兼容历史数据的函数，将旧格式转换为新格式
const compatibleHistoryData = data => {
  // 如果是新格式，直接返回
  if (data && typeof data === 'object' && !Array.isArray(data) && (data.aside || data.today)) {
    return {
      aside: data.aside || [],
      today: data.today || [],
    };
  }

  // 如果是旧格式（数组），转换为新格式
  if (Array.isArray(data)) {
    return {
      aside: data.filter(key => key >= 1 && key <= 9), // 两侧数据
      today: [], // 今日数据默认为空
    };
  }

  // 默认值
  return {
    aside: [1, 2, 3, 6, 7, 8],
    today: [],
  };
};

const SettingDispatch = ({ value, onChange }) => {
  const [checkedData, setCheckedData] = useState({ aside: [], today: [] });
  const [originalValue, setOriginalValue] = useState({ aside: [], today: [] }); // 保存原始值用于取消时重置
  const [visible, setVisible] = useState(false);

  const handleClick = key => {
    const isAsideKey = key >= 1 && key <= 9;
    const isTodayKey = key >= 10 && key <= 18;

    if (isAsideKey) {
      const newAside = checkedData.aside.includes(key)
        ? checkedData.aside.filter(item => item !== key)
        : [...checkedData.aside, key];
      setCheckedData({ ...checkedData, aside: newAside });
    } else if (isTodayKey) {
      const newToday = checkedData.today.includes(key)
        ? checkedData.today.filter(item => item !== key)
        : [...checkedData.today, key];
      setCheckedData({ ...checkedData, today: newToday });
    }
  };

  const onSubmit = () => {
    const totalSelected = checkedData.aside.length + checkedData.today.length;
    if (totalSelected < 6) {
      message.warning('请至少选择6项');
      return;
    }
    setVisible(false);
    const newData = {
      aside: checkedData.aside.sort((a, b) => a - b),
      today: checkedData.today.sort((a, b) => a - b),
    };
    setStorageSync(dashboardSettingKey, newData);
    onChange(newData);
    setOriginalValue(newData); // 更新原始值
  };

  const onCancel = () => {
    setVisible(false);
    setCheckedData(originalValue); // 重置为原始值
  };

  const onOpen = () => {
    setVisible(true);
    const compatibleData = compatibleHistoryData(value);
    setOriginalValue(compatibleData); // 保存当前值作为原始值
    setCheckedData(compatibleData); // 设置当前选中值
  };

  useEffect(
    () => {
      const compatibleValue = compatibleHistoryData(value);
      setCheckedData(compatibleValue);
      setOriginalValue(compatibleValue);
    },
    [value],
  );

  return (
    <Dropdown
      trigger={['click']}
      placement="bottomRight"
      visible={visible}
      overlay={
        <div style={{ backgroundColor: 'white', padding: 12, width: 400 }}>
          <Menu.Item>
            <Checkbox checked onClick={() => {}}>
              单独显示快递柜数据
            </Checkbox>
          </Menu.Item>
          <Row type="flex" justify="space-between">
            <Col span={12}>
              <div style={{ paddingLeft: 12 }}>两侧数据</div>
              <Menu>
                {list.map(item => (
                  <Menu.Item key={item.key}>
                    <Checkbox
                      checked={checkedData.aside.includes(item.key)}
                      onClick={() => handleClick(item.key)}
                    >
                      {item.title}
                    </Checkbox>
                  </Menu.Item>
                ))}
              </Menu>
            </Col>
            <Col span={12}>
              <div style={{ paddingLeft: 12 }}>今日数据</div>
              <Menu>
                {todayList.map(item => (
                  <Menu.Item key={item.key}>
                    <Checkbox
                      checked={checkedData.today.includes(item.key)}
                      onClick={() => handleClick(item.key)}
                    >
                      {item.title}
                    </Checkbox>
                  </Menu.Item>
                ))}
              </Menu>
            </Col>
          </Row>
          <Row type="flex" justify="center" gutter={12}>
            <Col>
              <Button onClick={onCancel}>取消</Button>
            </Col>
            <Col>
              <Button
                type="primary"
                onClick={onSubmit}
                disabled={checkedData.aside.length + checkedData.today.length < 6}
              >
                确定
              </Button>
            </Col>
          </Row>
        </div>
      }
    >
      <Button size="small" icon="setting" onClick={onOpen} />
    </Dropdown>
  );
};

export default SettingDispatch;
