/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { setStorageSync } from '@/utils/storage';
import { Button, Checkbox, Col, Dropdown, Menu, message, Row } from 'antd';
import React, { useEffect, useState } from 'react';

const list = [
  { key: 1, title: '每日入库量' },
  { key: 2, title: '3日出库率' },
  { key: 3, title: '每日寄件量' },
  { key: 4, title: '每日新增站点数' },
  { key: 5, title: '驿站数量' },
  { key: 6, title: '3日出库排名' },
  { key: 7, title: '昨日入库品牌占比' },
  { key: 8, title: '昨日入库量排名' },
  { key: 9, title: '驿站监控视频' },
];

const todayList = [
  { key: 1, title: '今日入库量' },
  { key: 2, title: '今日驿站入库量' },
  { key: 3, title: '今日入柜量' },
  { key: 4, title: '今日到件量' },
  { key: 5, title: '今日派件量' },
  { key: 6, title: '今日寄件量' },
  { key: 7, title: '今日快递费' },
  { key: 8, title: '今日出库量' },
  { key: 9, title: '今日取件量' },
];
export const dashboardSettingKey = 'kb-dashboardSetting';
const SettingDispatch = ({ value, onChange }) => {
  const [checkedList, setCheckedList] = useState([]);
  const [visible, setVisible] = useState(false);

  const handleClick = key => {
    if (checkedList.includes(key)) {
      setCheckedList(checkedList.filter(item => item !== key));
    } else {
      setCheckedList([...checkedList, key]);
    }
  };

  const onSubmit = () => {
    if (checkedList.length < 6) {
      message.warning('请至少选择6项');
      return;
    }
    setVisible(false);
    const _checkedList = checkedList.sort((a, b) => a - b);
    setStorageSync(dashboardSettingKey, _checkedList);
    onChange(_checkedList);
  };

  useEffect(
    () => {
      setCheckedList(value);
    },
    [value],
  );

  return (
    <Dropdown
      trigger={['click']}
      placement="bottomRight"
      visible={visible}
      overlay={
        <div style={{ backgroundColor: 'white', padding: 12, width: 400 }}>
          <Row type="flex" justify="space-between">
            <Col span={12}>
              <div style={{ paddingLeft: 12 }}>两侧数据</div>
              <Menu>
                {list.map(item => (
                  <Menu.Item key={item.key}>
                    <Checkbox
                      checked={checkedList.includes(item.key)}
                      onClick={() => handleClick(item.key)}
                    >
                      {item.title}
                    </Checkbox>
                  </Menu.Item>
                ))}
              </Menu>
            </Col>
            <Col span={12}>
              <div style={{ paddingLeft: 12 }}>今日数据</div>
              <Menu>
                {todayList.map(item => (
                  <Menu.Item key={item.key}>
                    <Checkbox
                      checked={checkedList.includes(item.key)}
                      onClick={() => handleClick(item.key)}
                    >
                      {item.title}
                    </Checkbox>
                  </Menu.Item>
                ))}
              </Menu>
            </Col>
          </Row>
          <Row type="flex" justify="center" gutter={12}>
            <Col>
              <Button onClick={() => setVisible(false)}>取消</Button>
            </Col>
            <Col>
              <Button type="primary" onClick={onSubmit} disabled={checkedList.length < 6}>
                确定
              </Button>
            </Col>
          </Row>
        </div>
      }
    >
      <Button size="small" icon="setting" onClick={() => setVisible(!visible)} />
    </Dropdown>
  );
};

export default SettingDispatch;
