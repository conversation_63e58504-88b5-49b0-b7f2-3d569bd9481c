/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import React from 'react';
import OutRateRange from '../OutRateRange';
import InStorageNumber from '../InStorageNumber';
import OutRate from '../OutRate';
import YzYesterdayInNum from '../YzYesterdayInNum';
import YzBrandsRate from '../YzBrandsRate';
import StationNum from '../StationNum';

// 映射新的today列表key到对应的组件类型
const getTodayComponentType = (num) => {
  switch (num) {
    case 10: return { type: 'InStorageNumber', props: { type: 'in' } }; // 今日入库量
    case 11: return { type: 'InStorageNumber', props: { type: 'in' } }; // 今日驿站入库量
    case 12: return { type: 'InStorageNumber', props: { type: 'in' } }; // 今日入柜量
    case 13: return { type: 'InStorageNumber', props: { type: 'in' } }; // 今日到件量
    case 14: return { type: 'InStorageNumber', props: { type: 'out' } }; // 今日派件量
    case 15: return { type: 'InStorageNumber', props: { type: 'out' } }; // 今日寄件量
    case 16: return { type: 'InStorageNumber', props: { type: 'out' } }; // 今日快递费
    case 17: return { type: 'InStorageNumber', props: { type: 'out' } }; // 今日出库量
    case 18: return { type: 'InStorageNumber', props: { type: 'out' } }; // 今日取件量
    default: return null;
  }
};

const ComDispatch = props => {
  const { num, ...rest } = props;

  // 处理新的today列表key (10-18)
  if (num >= 10 && num <= 18) {
    const componentConfig = getTodayComponentType(num);
    if (componentConfig && componentConfig.type === 'InStorageNumber') {
      return <InStorageNumber {...componentConfig.props} {...rest} />;
    }
    return null;
  }

  // 原有的逻辑保持不变 (1-9)
  return num == 1 || num == 3 || num == 4 ? (
    <InStorageNumber type={num == 1 ? 'in' : num == 3 ? 'out' : 'station'} {...rest} />
  ) : num == 2 ? (
    <OutRate {...rest} />
  ) : num == 5 ? (
    <StationNum {...rest} />
  ) : num == 6 ? (
    <OutRateRange {...rest} />
  ) : num == 7 ? (
    <YzBrandsRate title="昨日入库品牌占比" isPost={false} {...rest} />
  ) : num == 8 ? (
    <YzYesterdayInNum {...rest} />
  ) : null;
};

export default ComDispatch;
