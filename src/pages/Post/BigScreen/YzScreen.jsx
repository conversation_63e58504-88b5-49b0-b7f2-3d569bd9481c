/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 * 新零售大屏数据
 *  */
import React, { useEffect, useState } from 'react';
import { Row, Col } from 'antd';
import { connect } from 'dva';
import YzRealTimeArea from './components/YzRealTimeArea';
import SliderChart from './components/SliderChart';
import OutRateRange from './components/OutRateRange';
import InStorageNumber from './components/InStorageNumber';
import styles from './index.less';
import { registerShape } from './components/_utils';
import ComDispatch from './components/CommonComp/comDispatch';
import SettingDispatch, { dashboardSettingKey } from './components/CommonComp/settingDispatch';
import { getStorageSync } from '@/utils/storage';
// import { createPortal } from 'react-dom';

registerShape('round-rect');

function YzScreen({ options, isFullscreen }) {
  const { screenConfig = {} } = options || {};
  const [checkedList, setCheckedList] = useState([1, 2, 3, 6, 7, 8]);

  useEffect(() => {
    const { data } = getStorageSync(dashboardSettingKey) || {};
    if (data) {
      setCheckedList(data);
    }
  }, []);

  return (
    <>
      <div style={{ position: 'absolute', top: 10, right: 100 }}>
        <SettingDispatch value={checkedList} onChange={setCheckedList} />
      </div>
      <Row className={styles.body} style={{ flex: 1 }} type="flex">
        <Col span={6}>
          <Row className={styles['body-left']} gutter={[0, 20]} type="flex" justify="space-between">
            <Col span={24} style={{ flex: 1, position: 'relative' }}>
              <ComDispatch num={checkedList[0]} isParentFull={isFullscreen} />
            </Col>
            <Col span={24} style={{ flex: 1 }}>
              <ComDispatch num={checkedList[1]} isParentFull={isFullscreen} />
            </Col>
            <Col span={24} style={{ flex: 1 }}>
              {screenConfig.chart_left_3 === 'rank_3' ? (
                <OutRateRange isParentFull={isFullscreen} />
              ) : (
                <ComDispatch num={checkedList[2]} isParentFull={isFullscreen} />
              )}
            </Col>
          </Row>
        </Col>
        <Col span={12}>
          <Row
            className={styles['body-center']}
            gutter={[0, 20]}
            type="flex"
            justify="space-between"
          >
            <Col span={24}>
              <YzRealTimeArea />
            </Col>
            <Col span={24} style={{ flex: 1 }}>
              <SliderChart isParentFull={isFullscreen} />
            </Col>
          </Row>
        </Col>
        <Col span={6}>
          <Row
            className={styles['body-right']}
            gutter={[0, 20]}
            type="flex"
            justify="space-between"
          >
            <Col span={24} style={{ flex: 1 }}>
              {screenConfig.chart_right_1 ? (
                <InStorageNumber
                  type={screenConfig.chart_right_1 || 'send'}
                  isParentFull={isFullscreen}
                />
              ) : (
                <ComDispatch num={checkedList[3]} isParentFull={isFullscreen} />
              )}
            </Col>
            <Col span={24} style={{ flex: 1 }}>
              <ComDispatch num={checkedList[4]} isParentFull={isFullscreen} />
            </Col>
            <Col span={24} style={{ flex: 1 }}>
              <ComDispatch num={checkedList[5]} isParentFull={isFullscreen} />
            </Col>
          </Row>
        </Col>
      </Row>
    </>
  );
}

export default connect(({ global }) => ({
  screenToken: global.screenToken,
  screenLoginInfo: global.screenLoginInfo,
}))(YzScreen);
