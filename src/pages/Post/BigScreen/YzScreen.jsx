/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 * 新零售大屏数据
 *  */
import React, { useEffect, useState } from 'react';
import { Row, Col } from 'antd';
import { connect } from 'dva';
import YzRealTimeArea from './components/YzRealTimeArea';
import SliderChart from './components/SliderChart';
import OutRateRange from './components/OutRateRange';
import InStorageNumber from './components/InStorageNumber';
import styles from './index.less';
import { registerShape } from './components/_utils';
import ComDispatch from './components/CommonComp/comDispatch';
import SettingDispatch, { dashboardSettingKey } from './components/CommonComp/settingDispatch';
import { getStorageSync } from '@/utils/storage';
// import { createPortal } from 'react-dom';

registerShape('round-rect');

function YzScreen({ options, isFullscreen }) {
  const { screenConfig = {} } = options || {};
  const [checkedData, setCheckedData] = useState({ aside: [1, 2, 3, 6, 7, 8], today: [] });

  useEffect(() => {
    const { data } = getStorageSync(dashboardSettingKey) || {};
    if (data) {
      // 兼容新旧数据格式
      if (Array.isArray(data)) {
        // 旧格式，转换为新格式
        setCheckedData({ aside: data, today: [] });
      } else if (data && typeof data === 'object' && (data.aside || data.today)) {
        // 新格式
        setCheckedData({ aside: data.aside || [], today: data.today || [] });
      }
    }
  }, []);

  return (
    <>
      <div style={{ position: 'absolute', top: 10, right: 100 }}>
        <SettingDispatch value={checkedData} onChange={setCheckedData} />
      </div>
      <Row className={styles.body} style={{ flex: 1 }} type="flex">
        <Col span={6}>
          <Row className={styles['body-left']} gutter={[0, 20]} type="flex" justify="space-between">
            <Col span={24} style={{ flex: 1, position: 'relative' }}>
              <ComDispatch num={checkedData.aside[0]} isParentFull={isFullscreen} />
            </Col>
            <Col span={24} style={{ flex: 1 }}>
              <ComDispatch num={checkedData.aside[1]} isParentFull={isFullscreen} />
            </Col>
            <Col span={24} style={{ flex: 1 }}>
              {screenConfig.chart_left_3 === 'rank_3' ? (
                <OutRateRange isParentFull={isFullscreen} />
              ) : (
                <ComDispatch num={checkedData.aside[2]} isParentFull={isFullscreen} />
              )}
            </Col>
          </Row>
        </Col>
        <Col span={12}>
          <Row
            className={styles['body-center']}
            gutter={[0, 20]}
            type="flex"
            justify="space-between"
          >
            <Col span={24}>
              <YzRealTimeArea />
            </Col>
            <Col span={24} style={{ flex: 1 }}>
              <SliderChart isParentFull={isFullscreen} />
            </Col>
          </Row>
        </Col>
        <Col span={6}>
          <Row
            className={styles['body-right']}
            gutter={[0, 20]}
            type="flex"
            justify="space-between"
          >
            <Col span={24} style={{ flex: 1 }}>
              {screenConfig.chart_right_1 ? (
                <InStorageNumber
                  type={screenConfig.chart_right_1 || 'send'}
                  isParentFull={isFullscreen}
                />
              ) : (
                <ComDispatch num={checkedData.aside[3]} isParentFull={isFullscreen} />
              )}
            </Col>
            <Col span={24} style={{ flex: 1 }}>
              <ComDispatch num={checkedData.aside[4]} isParentFull={isFullscreen} />
            </Col>
            <Col span={24} style={{ flex: 1 }}>
              <ComDispatch num={checkedData.aside[5]} isParentFull={isFullscreen} />
            </Col>
          </Row>
        </Col>
      </Row>
    </>
  );
}

export default connect(({ global }) => ({
  screenToken: global.screenToken,
  screenLoginInfo: global.screenLoginInfo,
}))(YzScreen);
